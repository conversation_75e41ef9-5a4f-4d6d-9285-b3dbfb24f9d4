{"name": "immersive-translate", "version": "1.19.6", "description": "Immersive Translate - Web & PDF Translation Extension", "main": "dist/background.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "build:dev": "webpack --mode development", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "zip": "npm run build && cd dist && zip -r ../immersive-translate.zip ."}, "keywords": ["translation", "chrome-extension", "browser-extension", "immersive", "bilingual"], "author": "Immersive Translate Team", "license": "MIT", "devDependencies": {"@types/chrome": "^0.0.268", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.8.1", "rimraf": "^5.0.5", "style-loader": "^3.3.4", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "typescript": "^5.4.2", "webpack": "^5.90.3", "webpack-cli": "^5.1.4"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "tesseract.js": "^5.1.1"}}